# Tournament Organizer Feature - Task Document

## Overview
This document provides a structured task breakdown for implementing the Tournament Organizer feature. All tasks are organized hierarchically and should be completed in the order specified to ensure proper dependencies are met.

## Task Structure

### 🎯 Main Goal: Tournament Organizer Feature Implementation
**Status**: Not Started  
**Description**: Implement the complete Tournament Organizer feature for admin users as outlined in the planning document

---

## Phase 1: Database Schema Setup
**Priority**: High (Must be completed first)  
**Estimated Time**: 2-3 days

### 1.1 Create new database tables migration
- **File**: `supabase/migrations/YYYYMMDD_tournament_organizer_tables.sql`
- **Tables to create**:
  - `tournament_organizer_settings`
  - `tournament_rounds` 
  - `tournament_pairings`
  - `tournament_player_stats`
- **Dependencies**: None
- **Validation**: Tables created with proper constraints and relationships

### 1.2 Add columns to tournaments table
- **File**: Same migration or separate file
- **Changes**:
  - `has_organizer BOOLEAN DEFAULT FALSE`
  - `organizer_created_at TIMESTAMP WITH TIME ZONE`
- **Dependencies**: 1.1 completed
- **Validation**: Columns added without breaking existing functionality

### 1.3 Implement RLS policies
- **File**: Same migration or separate file
- **Policies needed**:
  - Admin-only access during tournament
  - Public read access after completion
  - Leverage existing `is_admin()` function
- **Dependencies**: 1.1, 1.2 completed
- **Validation**: Admin can access, non-admin cannot during active tournament

### 1.4 Test database schema
- **Actions**:
  - Apply migration to development environment
  - Test admin access patterns
  - Verify constraint enforcement
  - Test RLS policies
- **Dependencies**: 1.1, 1.2, 1.3 completed
- **Validation**: All database operations work as expected

---

## Phase 2: UI Components Development
**Priority**: High  
**Estimated Time**: 5-7 days

### 2.1 Tournament creation button
- **File**: `src/components/calendar/TournamentDetails/TournamentDetails.tsx`
- **Requirements**:
  - Green square button with "+" icon
  - Admin-only visibility using `useIsAdmin()`
  - Tooltip "create tournament"
  - State change to "eye" icon after creation
- **Dependencies**: Phase 1 completed
- **Validation**: Button appears only for admins, correct styling

### 2.2 Confirmation modal
- **File**: `src/components/calendar/TournamentDetails/TournamentCreationModal.tsx`
- **Requirements**:
  - Use existing `Modal` component
  - "Do you want to create a tournament for this event?" text
  - Cancel (blue) and Proceed (green) buttons
  - Consistent styling with existing modals
- **Dependencies**: 2.1 completed
- **Validation**: Modal opens/closes correctly, proper button actions

### 2.3 Tournament setup modal
- **File**: `src/components/calendar/TournamentDetails/TournamentSetupModal.tsx`
- **Requirements**:
  - Number of rounds input (1-10 range)
  - Round duration input (30-120 minutes)
  - Form validation using existing `FormField` and `Input`
  - Save settings to `tournament_organizer_settings`
- **Dependencies**: 2.2 completed
- **Validation**: Form validation works, settings saved correctly

### 2.4 Tournament organizer panel
- **File**: `src/components/calendar/TournamentDetails/TournamentOrganizerPanel.tsx`
- **Requirements**:
  - Fullscreen panel (`size="full"`)
  - Close "X" button
  - Settings gear icon
  - Layout: player list (left), pairings (center), controls (right)
- **Dependencies**: 2.3 completed
- **Validation**: Panel opens fullscreen, proper layout on desktop/mobile

### 2.5 Timer component
- **File**: `src/components/calendar/TournamentDetails/TournamentTimer.tsx`
- **Requirements**:
  - Large MM:SS display at top center
  - Color changes (green → yellow → red)
  - Start/pause/reset controls
  - Integration with round duration settings
- **Dependencies**: 2.4 completed
- **Validation**: Timer functions correctly, visual indicators work

### 2.6 Pairing interface
- **File**: `src/components/calendar/TournamentDetails/PairingInterface.tsx`
- **Requirements**:
  - Match cards showing player pairings
  - Table numbers
  - Bye indicators
  - Generate/Confirm/Cancel pairing buttons
- **Dependencies**: 2.4 completed
- **Validation**: Pairings display correctly, buttons function

### 2.7 Results entry interface
- **File**: `src/components/calendar/TournamentDetails/ResultsInterface.tsx`
- **Requirements**:
  - Result dropdowns: 2-0, 2-1, 1-2, 0-2, 1-0, 0-1, 0-0
  - Bulk result entry
  - Result validation
  - Final standings table
- **Dependencies**: 2.6 completed
- **Validation**: Results can be entered and saved correctly

---

## Phase 3: Business Logic Implementation
**Priority**: High  
**Estimated Time**: 4-6 days

### 3.1 Tournament services
- **File**: `src/lib/services/tournamentOrganizer.ts`
- **Requirements**:
  - CRUD operations for all new tables
  - Integration with existing Supabase client
  - Error handling and validation
  - TypeScript types
- **Dependencies**: Phase 1 completed
- **Validation**: All database operations work through service layer

### 3.2 Swiss pairing algorithm
- **File**: `src/lib/utils/swissPairing.ts`
- **Requirements**:
  - Random first round pairing
  - Point-based subsequent rounds
  - Bye assignment logic
  - Avoid repeat pairings
  - Handle odd number of players
- **Dependencies**: 3.1 completed
- **Validation**: Algorithm produces valid pairings for various scenarios

### 3.3 Round management system
- **File**: `src/lib/utils/roundManagement.ts`
- **Requirements**:
  - Round progression logic
  - State transitions (pending → active → completed)
  - Timer integration
  - Round validation
- **Dependencies**: 3.2 completed
- **Validation**: Rounds progress correctly, state management works

### 3.4 Scoring and statistics
- **File**: `src/lib/utils/tournamentScoring.ts`
- **Requirements**:
  - Match point calculation (3-1-0 system)
  - Game win/loss tracking
  - OMW% and GWP% calculation
  - Final standings generation
- **Dependencies**: 3.3 completed
- **Validation**: Scoring calculations are accurate

### 3.5 Real-time updates
- **File**: `src/lib/hooks/useTournamentSubscription.ts`
- **Requirements**:
  - Supabase subscriptions for live updates
  - React Query integration
  - Optimistic updates
  - Connection handling
- **Dependencies**: 3.4 completed
- **Validation**: Real-time updates work across multiple clients

---

## Phase 4: Integration & Testing
**Priority**: Medium  
**Estimated Time**: 3-4 days

### 4.1 Integration with existing tournament system
- **Files**: Various existing tournament components
- **Requirements**:
  - Connect with existing tournament data
  - Integrate with registration system
  - Maintain backward compatibility
  - Update existing hooks if needed
- **Dependencies**: Phase 2, 3 completed
- **Validation**: Existing functionality unaffected

### 4.2 Admin permission testing
- **Requirements**:
  - Test all admin-only features
  - Verify RLS policies work
  - Test unauthorized access attempts
  - Validate security restrictions
- **Dependencies**: All phases completed
- **Validation**: Security requirements met

### 4.3 End-to-end tournament flow testing
- **Requirements**:
  - Test complete tournament from creation to results
  - Multiple scenarios (different player counts, rounds)
  - Error scenarios and edge cases
  - Data consistency validation
- **Dependencies**: 4.1, 4.2 completed
- **Validation**: Full tournament flow works correctly

### 4.4 Mobile responsiveness testing
- **Requirements**:
  - Test all components on mobile devices
  - Verify touch interactions
  - Check responsive layouts
  - Performance on mobile
- **Dependencies**: 4.3 completed
- **Validation**: Mobile experience is usable

### 4.5 Performance optimization
- **Requirements**:
  - Optimize for large tournaments
  - Implement caching strategies
  - Database query optimization
  - Bundle size considerations
- **Dependencies**: 4.4 completed
- **Validation**: Performance meets requirements

---

## Success Criteria

### Functional Requirements
- ✅ Admin can create tournament organizer for any event
- ✅ Tournament setup with configurable rounds and duration
- ✅ Swiss pairing system works correctly
- ✅ Round management with timer functionality
- ✅ Result entry and final standings generation
- ✅ Real-time updates for multiple admins

### Technical Requirements
- ✅ All new database tables and RLS policies implemented
- ✅ Integration with existing codebase maintained
- ✅ Mobile responsive design
- ✅ Performance optimized for tournaments up to 50+ players
- ✅ Security requirements met (admin-only during tournament)

### Quality Requirements
- ✅ Code follows existing patterns and conventions
- ✅ Comprehensive error handling
- ✅ TypeScript types for all new code
- ✅ Consistent UI/UX with existing components
- ✅ Thorough testing coverage

---

## Notes
- Each task should be completed and tested before moving to the next
- Regular testing against the planning document requirements
- Consider creating feature flags for gradual rollout
- Document any deviations from the original plan
